import { useState, useCallback } from 'react';
import { Plus, AlertCircle } from 'lucide-react';
import { validateStockCode, debounce } from '@/utils/validation';
import { stockDataApi } from '@/services/stockApi';

interface StockInputProps {
  onAddStock: (code: string, name?: string) => Promise<{ success: boolean; message?: string }>;
  isLoading?: boolean;
  isFullScreen?: boolean;
}

export function StockInput({ onAddStock, isLoading = false, isFullScreen = false }: StockInputProps) {
  const [inputValue, setInputValue] = useState('');
  const [validationError, setValidationError] = useState<string | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  // 防抖验证函数
  const debouncedValidate = useCallback(
    debounce((code: string) => {
      if (!code.trim()) {
        setValidationError(null);
        setIsValidating(false);
        return;
      }

      const validation = validateStockCode(code);
      setValidationError(validation.isValid ? null : validation.message || '无效的股票代码');
      setIsValidating(false);
    }, 300),
    []
  );

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    // 只允许输入数字，最多6位
    const numericValue = value.replace(/\D/g, '').slice(0, 6);
    setInputValue(numericValue);
    
    // 开始验证
    if (numericValue.trim()) {
      setIsValidating(true);
      debouncedValidate(numericValue);
    } else {
      setValidationError(null);
      setIsValidating(false);
    }
  };

  // 处理添加股票
  const handleAddStock = async () => {
    if (!inputValue.trim() || validationError || isLoading) {
      return;
    }

    try {
      // 先获取股票数据以验证代码有效性并获取股票名称
      const stockData = await stockDataApi.getStockData(inputValue, 1);
      const stockName = stockData.summary?.name || `股票${inputValue}`;

      // 调用添加股票API
      const result = await onAddStock(inputValue, stockName);

      if (result.success) {
        setInputValue('');
        setValidationError(null);
      } else {
        setValidationError(result.message || '添加失败');
      }
    } catch (error) {
      // 如果获取股票信息失败，尝试使用默认名称添加
      console.warn('获取股票信息失败，使用默认名称:', error);
      const result = await onAddStock(inputValue);

      if (result.success) {
        setInputValue('');
        setValidationError(null);
      } else {
        setValidationError('股票代码无效或获取信息失败');
      }
    }
  };

  // 处理回车键
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddStock();
    }
  };

  const hasError = validationError !== null;
  const canAdd = inputValue.trim() && !hasError && !isValidating && !isLoading;

  return (
    <div className="space-y-3">
      <div className="flex gap-2">
        <div className="flex-1">
          <input
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder="输入6位股票代码"
            className={`input ${isFullScreen ? 'text-lg py-3' : ''} ${hasError ? 'border-danger-500 focus:ring-danger-500' : ''}`}
            disabled={isLoading}
            maxLength={6}
          />

          {/* 验证状态指示 */}
          {isValidating && (
            <div className={`mt-1 ${isFullScreen ? 'text-base' : 'text-sm'} text-gray-500`}>
              验证中...
            </div>
          )}

          {/* 错误信息 */}
          {hasError && (
            <div className={`mt-1 flex items-center gap-1 ${isFullScreen ? 'text-base' : 'text-sm'} text-danger-600`}>
              <AlertCircle className={`${isFullScreen ? 'w-5 h-5' : 'w-4 h-4'}`} />
              <span>{validationError}</span>
            </div>
          )}
        </div>

        <button
          onClick={handleAddStock}
          disabled={!canAdd}
          className={`btn ${canAdd ? 'btn-primary' : 'btn-secondary'} flex items-center gap-2 ${isFullScreen ? 'px-6 py-3 text-lg' : 'px-4 py-2'}`}
        >
          {isLoading ? (
            <div className={`${isFullScreen ? 'w-5 h-5' : 'w-4 h-4'} border-2 border-white border-t-transparent rounded-full animate-spin`} />
          ) : (
            <Plus className={`${isFullScreen ? 'w-5 h-5' : 'w-4 h-4'}`} />
          )}
          添加
        </button>
      </div>

      {/* 输入提示 */}
      <div className={`${isFullScreen ? 'text-sm' : 'text-xs'} text-gray-500`}>
        支持的股票代码：沪市(600xxx)、深市(000xxx)、创业板(300xxx)、科创板(688xxx)等
      </div>
    </div>
  );
}
