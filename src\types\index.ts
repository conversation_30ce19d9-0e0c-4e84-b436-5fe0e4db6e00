// 股票基础信息
export interface Stock {
  code: string;
  name: string;
}

// 资金流向数据
export interface StockFlowData {
  code: string;
  name: string;
  timestamp: string;
  
  // 主力资金
  mainInflow: number;
  mainOutflow: number;
  mainNetInflow: number;
  mainNetRatio: number;
  
  // 超大单资金
  superLargeInflow: number;
  superLargeOutflow: number;
  superLargeNetInflow: number;
  superLargeNetRatio: number;
  
  // 大单资金
  largeInflow: number;
  largeOutflow: number;
  largeNetInflow: number;
  largeNetRatio: number;
  
  // 中单资金
  mediumInflow: number;
  mediumOutflow: number;
  mediumNetInflow: number;
  mediumNetRatio: number;
  
  // 小单资金
  smallInflow: number;
  smallOutflow: number;
  smallNetInflow: number;
  smallNetRatio: number;
}

// 时间序列数据点
export interface TimeSeriesDataPoint {
  time: string;
  value: number;
  type: 'main' | 'superLarge' | 'large' | 'medium' | 'small';
}

// 图表数据
export interface ChartData {
  timeData: string[];
  mainData: number[];
  superLargeData: number[];
  largeData: number[];
  mediumData: number[];
  smallData: number[];
}

// API响应格式
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

// 错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

// 股票代码验证结果
export interface ValidationResult {
  isValid: boolean;
  message?: string;
}

// 应用状态
export interface AppState {
  stocks: Stock[];
  selectedStock: string | null;
  isLoading: boolean;
  error: string | null;
  lastUpdate: string | null;
  autoRefresh: boolean;
}
